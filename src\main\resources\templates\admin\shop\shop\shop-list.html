<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shop Management - ReadHub</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #F8F5F0;
            color: #333;
        }
        .navbar-brand {
            font-family: 'Lora', serif;
            font-weight: 700;
            font-size: 1.8rem;
            color: #2C3E50;
        }
        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #2C3E50;
            border-bottom: 2px solid #2C3E50;
            padding-bottom: 0.5rem;
        }
        .account-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .default-profile {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #E9ECEF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .btn-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
        }
        .account-sidebar {
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }
        .account-sidebar .list-group-item {
            border: none;
            border-left: 4px solid transparent;
            font-weight: 600;
        }
        .account-sidebar .list-group-item.active {
            background-color: #f8f9fa;
            color: #2C3E50;
            border-left: 4px solid #2C3E50;
        }
        .account-sidebar .list-group-item i {
            margin-right: 10px;
            color: #6c757d;
        }
        .account-sidebar .list-group-item.active i {
            color: #2C3E50;
        }
        .card {
            border: none;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
        }
    </style>
</head>
<body>
<div th:replace="~{fragments/admin-topbar :: admin-topbar}"></div>

<div class="container my-4">
    <div class="row">
        <div class="col-lg-3 mb-4">
            <div th:replace="~{fragments/admin-sidebar :: admin-sidebar(activeMenu='shops')}"></div>
        </div>

        <div class="col-lg-9">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">Shop Management</h2>
            </div>

            <div class="card mb-4">
                <div class="card-body">
                    <form th:action="@{/admin/shops}" method="get" class="row g-3">
                        <div class="col-md-5">
                            <input type="text" class="form-control" name="keyword" th:value="${keyword}" placeholder="Search by Shop Name or Email...">
                        </div>
                        <div class="col-md-4">
                            <select name="status" class="form-select">
                                <option value="all" th:selected="${statusFilter == 'all' or statusFilter == null}">All Statuses</option>
                                <option value="APPROVED" th:selected="${statusFilter == 'APPROVED'}">Approved</option>
                                <option value="PENDING" th:selected="${statusFilter == 'PENDING'}">Pending</option>
                                <option value="REJECTED" th:selected="${statusFilter == 'REJECTED'}">Rejected</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-primary w-100">Filter</button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                            <tr>
                                <th class="py-3 px-4">ID</th>
                                <th class="py-3 px-4">Logo</th>
                                <th class="py-3 px-4">Shop Name</th>
                                <th class="py-3 px-4">Owner</th>
                                <th class="py-3 px-4">Status</th>
                                <th class="py-3 px-4">Registration Date</th>
                                <th class="py-3 px-4">Actions</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr th:each="shop : ${shopPage.content}">
                                <td class="align-middle py-3 px-4" th:text="${shop.shopId}"></td>
                                <td class="align-middle py-3 px-4"><img th:src="${shop.logoUrl ?: '/images/default-shop-logo.png'}" alt="Logo" width="40" height="40" class="rounded"></td>
                                <td class="align-middle py-3 px-4" th:text="${shop.shopName}"></td>
                                <td class="align-middle py-3 px-4" th:text="${shop.user.fullName}"></td>
                                <td class="align-middle py-3 px-4">
                                    <span th:text="${shop.approvalStatus.name()}"
                                          th:classappend="${shop.approvalStatus.name() == 'APPROVED' ? 'badge bg-success' : (shop.approvalStatus.name() == 'PENDING' ? 'badge bg-warning text-dark' : 'badge bg-danger')}">
                                    </span>
                                </td>
                                <td class="align-middle py-3 px-4" th:text="${#temporals.format(shop.registrationDate, 'dd-MM-yyyy')}"></td>
                                <td class="align-middle py-3 px-4">
                                    <a th:href="@{/admin/shops/detail/{id}(id=${shop.shopId})}" class="btn btn-sm btn-info" title="View Details"><i class="fas fa-eye"></i></a>
                                    <a th:href="@{/admin/seller-approvals}" class="btn btn-sm btn-primary" title="Edit"><i class="fas fa-edit"></i></a>
                                    <button type="button" class="btn btn-sm btn-warning" title="Block Shop & Revert to Buyer"
                                            th:if="${shop.active}"
                                            th:onclick="'confirmAndBlockShop(' + ${shop.shopId} + ')'">
                                        <i class="fas fa-ban"></i>
                                    </button>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer bg-white" th:if="${shopPage.totalPages > 1}">
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center mb-0">
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

<script>
    function confirmAndBlockShop(shopId) {
        if (confirm('Are you sure you want to block this shop? This will revert the owner\\\'s role to BUYER and deactivate the shop.')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/admin/shops/block/${shopId}`;

            const csrfToken = document.querySelector('meta[name="_csrf"]');
            const csrfHeader = document.querySelector('meta[name="_csrf_header"]');

            if (csrfToken && csrfHeader) {
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = csrfHeader.content;
                csrfInput.value = csrfToken.content;
                form.appendChild(csrfInput);
            }

            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
</body>
</html>