<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Sidebar Fragment</title>
</head>
<body>
<div th:fragment="admin-sidebar(activeMenu)" class="account-sidebar">
    <div class="p-4 text-center border-bottom">
        <div th:if="${user.profilePicUrl != null and !user.profilePicUrl.isEmpty()}">
            <img th:src="${user.profilePicUrl}" alt="Profile Picture" class="profile-image mb-3">
        </div>
        <div th:unless="${user.profilePicUrl != null and !user.profilePicUrl.isEmpty()}" class="default-profile mx-auto mb-3">
            <i class="fas fa-user"></i>
        </div>
        <h5 class="mb-1" th:text="${user.fullName}">Admin Name</h5>
        <p class="text-muted mb-1" th:text="${user.email}"><EMAIL></p>
        <div class="d-flex justify-content-center mt-2">
            <span th:each="roleName : ${roles}" class="badge bg-primary me-1" th:text="${roleName.replace('ROLE_', '')}">ADMIN</span>
        </div>
    </div>
    <div class="list-group list-group-flush">
        <!-- Dashboard -->
        <a th:href="@{/admin/dashboard}" class="list-group-item list-group-item-action" th:classappend="${activeMenu == 'dashboard' ? 'active' : ''}">
            <i class="fas fa-chart-line"></i> Dashboard
        </a>

        <!-- User Management -->
        <a th:href="@{/admin/users}" class="list-group-item list-group-item-action" th:classappend="${activeMenu == 'users' ? 'active' : ''}">
            <i class="fas fa-users"></i> User Management
        </a>

        <!-- Seller Management -->
        <a th:href="@{/admin/shops}" class="list-group-item list-group-item-action" th:classappend="${activeMenu == 'shops' ? 'active' : ''}">
            <i class="fas fa-store-alt"></i> Shop Management
        </a>
        <!-- Product Management -->
        <a th:href="@{/admin/products}" class="list-group-item list-group-item-action" th:classappend="${activeMenu == 'products' ? 'active' : ''}">
            <i class="fas fa-book"></i> Product Management
        </a>

        <!-- Order Management -->
        <a th:href="@{/admin/orders}" class="list-group-item list-group-item-action" th:classappend="${activeMenu == 'orders' ? 'active' : ''}">
            <i class="fas fa-shopping-cart"></i> Order Management
        </a>

        <!-- Category Management -->
        <a th:href="@{/admin/categories}" class="list-group-item list-group-item-action" th:classappend="${activeMenu == 'categories' ? 'active' : ''}">
            <i class="fas fa-tags"></i> Category Management
        </a>

        <!-- Promotion Management -->
        <a th:href="@{/admin/promotions}" class="list-group-item list-group-item-action" th:classappend="${activeMenu == 'promotions' ? 'active' : ''}">
            <i class="fas fa-percentage"></i> Promotion Management
        </a>

        <!-- Blog Management -->
        <a th:href="@{/admin/blogs}" class="list-group-item list-group-item-action" th:classappend="${activeMenu == 'blog' ? 'active' : ''}">
            <i class="fas fa-blog"></i> Blog Management
        </a>
        <a th:href="@{/admin/product-reviews}" class="list-group-item list-group-item-action" th:classappend="${activeMenu == 'product-reviews' ? 'active' : ''}">
            <i class="fas fa-star"></i> Product Reviews Moderation
        </a>
        <a th:href="@{/admin/blog-comments}" class="list-group-item list-group-item-action" th:classappend="${activeMenu == 'blog-comments' ? 'active' : ''}">
            <i class="fas fa-comments"></i> Blog Comments Moderation
        </a>

        <!-- Reports -->
        <a th:href="@{/admin/reports}" class="list-group-item list-group-item-action" th:classappend="${activeMenu == 'reports' ? 'active' : ''}">
            <i class="fas fa-chart-line"></i> Revenue Reports
        </a>

        <!-- Consolidated Reports -->
        <a th:href="@{/admin/consolidated-reports}" class="list-group-item list-group-item-action" th:classappend="${activeMenu == 'consolidated-reports' ? 'active' : ''}">
            <i class="fas fa-chart-bar"></i> Consolidated Reports
        </a>

        <!-- Settings -->
        <a th:href="@{/admin/settings}" class="list-group-item list-group-item-action" th:classappend="${activeMenu == 'settings' ? 'active' : ''}">
            <i class="fas fa-cog"></i> System Settings
        </a>

        <!-- Profile Settings -->

        <!-- Logout -->
        <a href="/logout" class="list-group-item list-group-item-action text-danger">
            <i class="fas fa-sign-out-alt"></i> Logout
        </a>
    </div>
</div>
</body>
</html>